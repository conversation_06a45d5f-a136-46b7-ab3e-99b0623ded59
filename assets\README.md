# Assets Directory

This directory contains all the assets for the Khatwa app.

## Required Files

### Images
- `assets/images/logo.png` - App logo (please add this file)

### Videos  
- `assets/videos/welcome.mp4` - Welcome screen background video (please add this file)

### Fonts
Please add the following Arabic font files to `assets/fonts/`:

#### Cairo Font
- `Cairo-Regular.ttf`
- `Cairo-Bold.ttf`
- `Cairo-Light.ttf`

#### <PERSON>i <PERSON>ont
- `Amiri-Regular.ttf`
- `Amiri-Bold.ttf`

#### Tajawal Font
- `Ta<PERSON>wal-Regular.ttf`
- `Tajawal-Bold.ttf`
- `Ta<PERSON>wal-Light.ttf`

## Font Sources

You can download these fonts from:
- **Cairo**: https://fonts.google.com/specimen/Cairo
- **Amiri**: https://fonts.google.com/specimen/Amiri
- **<PERSON><PERSON>wal**: https://fonts.google.com/specimen/Tajawal

## Instructions

1. Download the font files from Google Fonts
2. Extract the TTF files
3. Place them in the `assets/fonts/` directory with the exact names listed above
4. Add your logo.png to `assets/images/`
5. Add your welcome.mp4 to `assets/videos/`
6. Run `flutter pub get` to update dependencies
