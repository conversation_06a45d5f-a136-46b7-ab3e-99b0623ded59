1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.khatwa.app.khatwa_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[:file_picker] D:\Development\ Yassine\flutter\Khotwa\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] D:\Development\ Yassine\flutter\Khotwa\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
30-->[:file_picker] D:\Development\ Yassine\flutter\Khotwa\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
31
32            <data android:mimeType="*/*" />
32-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:42:13-50
32-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:42:19-48
33        </intent> <!-- Needs to be explicitly declared on Android R+ -->
34        <package android:name="com.google.android.apps.maps" />
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
35    </queries> <!-- Include required permissions for Google Maps API to run. -->
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
36-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
37
38    <uses-feature
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
39        android:glEsVersion="0x00020000"
39-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
40        android:required="true" />
40-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
41
42    <permission
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
43        android:name="com.khatwa.app.khatwa_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="com.khatwa.app.khatwa_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
47
48    <application
49        android:name="android.app.Application"
50        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
51        android:debuggable="true"
52        android:extractNativeLibs="true"
53        android:icon="@mipmap/ic_launcher"
54        android:label="khatwa_app" >
55        <activity
56            android:name="com.khatwa.app.khatwa_app.MainActivity"
57            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
58            android:exported="true"
59            android:hardwareAccelerated="true"
60            android:launchMode="singleTop"
61            android:taskAffinity=""
62            android:theme="@style/LaunchTheme"
63            android:windowSoftInputMode="adjustResize" >
64
65            <!--
66                 Specifies an Android theme to apply to this Activity as soon as
67                 the Android process has started. This theme is visible to the user
68                 while the Flutter UI initializes. After that, this theme continues
69                 to determine the Window background behind the Flutter UI.
70            -->
71            <meta-data
72                android:name="io.flutter.embedding.android.NormalTheme"
73                android:resource="@style/NormalTheme" />
74
75            <intent-filter>
76                <action android:name="android.intent.action.MAIN" />
77
78                <category android:name="android.intent.category.LAUNCHER" />
79            </intent-filter>
80        </activity>
81        <!--
82             Don't delete the meta-data below.
83             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
84        -->
85        <meta-data
86            android:name="flutterEmbedding"
87            android:value="2" />
88
89        <service
89-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
90            android:name="com.baseflow.geolocator.GeolocatorLocationService"
90-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
91            android:enabled="true"
91-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
92            android:exported="false"
92-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
93            android:foregroundServiceType="location" />
93-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
94
95        <provider
95-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
96            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
96-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
97            android:authorities="com.khatwa.app.khatwa_app.flutter.image_provider"
97-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
98            android:exported="false"
98-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
99            android:grantUriPermissions="true" >
99-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
100            <meta-data
100-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
101                android:name="android.support.FILE_PROVIDER_PATHS"
101-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
102                android:resource="@xml/flutter_image_picker_file_paths" />
102-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
103        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
104        <service
104-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
105            android:name="com.google.android.gms.metadata.ModuleDependencies"
105-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
106            android:enabled="false"
106-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
107            android:exported="false" >
107-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
108            <intent-filter>
108-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
109                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
109-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
109-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
110            </intent-filter>
111
112            <meta-data
112-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
113                android:name="photopicker_activity:0:required"
113-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
114                android:value="" />
114-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
115        </service>
116
117        <activity
117-->[:url_launcher_android] D:\Development\ Yassine\flutter\Khotwa\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
118            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
118-->[:url_launcher_android] D:\Development\ Yassine\flutter\Khotwa\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
119            android:exported="false"
119-->[:url_launcher_android] D:\Development\ Yassine\flutter\Khotwa\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
120            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
120-->[:url_launcher_android] D:\Development\ Yassine\flutter\Khotwa\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
121        <uses-library
121-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
122            android:name="org.apache.http.legacy"
122-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
123            android:required="false" />
123-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
124
125        <activity
125-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
126            android:name="com.google.android.gms.common.api.GoogleApiActivity"
126-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
127            android:exported="false"
127-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
128            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
128-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
129
130        <meta-data
130-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
131            android:name="com.google.android.gms.version"
131-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
132            android:value="@integer/google_play_services_version" />
132-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
133
134        <uses-library
134-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
135            android:name="androidx.window.extensions"
135-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
136            android:required="false" />
136-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
137        <uses-library
137-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
138            android:name="androidx.window.sidecar"
138-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
139            android:required="false" />
139-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
140
141        <provider
141-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
142            android:name="androidx.startup.InitializationProvider"
142-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
143            android:authorities="com.khatwa.app.khatwa_app.androidx-startup"
143-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
144            android:exported="false" >
144-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
145            <meta-data
145-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
146                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
146-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
147                android:value="androidx.startup" />
147-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
148            <meta-data
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
149                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
150                android:value="androidx.startup" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
151        </provider>
152
153        <receiver
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
154            android:name="androidx.profileinstaller.ProfileInstallReceiver"
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
155            android:directBootAware="false"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
156            android:enabled="true"
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
157            android:exported="true"
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
158            android:permission="android.permission.DUMP" >
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
160                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
161            </intent-filter>
162            <intent-filter>
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
163                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
164            </intent-filter>
165            <intent-filter>
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
166                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
167            </intent-filter>
168            <intent-filter>
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
169                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
170            </intent-filter>
171        </receiver>
172    </application>
173
174</manifest>
