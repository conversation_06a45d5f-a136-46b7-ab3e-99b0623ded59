{"logs": [{"outputFile": "com.khatwa.app.khatwa_app-mergeDebugResources-45:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4470", "endColumns": "128", "endOffsets": "4594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a3d296532cc5acbfa6d00cce05e38839\\transformed\\jetified-play-services-base-18.3.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3496,3602,3756,3881,3990,4131,4256,4365,4599,4753,4859,5016,5142,5284,5438,5502,5565", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "3597,3751,3876,3985,4126,4251,4360,4465,4748,4854,5011,5137,5279,5433,5497,5560,5639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66c3f8d759689e7c8bf8d566a47d4905\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5818,5885,5944,6003,6069,6145,6208,6297,6379", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "5880,5939,5998,6064,6140,6203,6292,6374,6443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,475,644,727", "endColumns": "69,86,77,134,168,82,79", "endOffsets": "170,257,335,470,639,722,802"}, "to": {"startLines": "54,65,69,70,73,74,75", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5644,6448,6843,6921,7237,7406,7489", "endColumns": "69,86,77,134,168,82,79", "endOffsets": "5709,6530,6916,7051,7401,7484,7564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,875,966,1058,1151,1245,1352,1445,1540,1635,1726,1820,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,79,90,91,92,93,106,92,94,94,90,93,79,109,106,96,108,99,102,154,97,79", "endOffsets": "200,297,409,494,595,709,790,870,961,1053,1146,1240,1347,1440,1535,1630,1721,1815,1895,2005,2112,2209,2318,2418,2521,2676,2774,2854"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,875,966,1058,1151,1245,1352,1445,1540,1635,1726,1820,1900,2010,2117,2214,2323,2423,2526,2681,7056", "endColumns": "99,96,111,84,100,113,80,79,90,91,92,93,106,92,94,94,90,93,79,109,106,96,108,99,102,154,97,79", "endOffsets": "200,297,409,494,595,709,790,870,961,1053,1146,1240,1347,1440,1535,1630,1721,1815,1895,2005,2112,2209,2318,2418,2521,2676,2774,7131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "55,66,67,68", "startColumns": "4,4,4,4", "startOffsets": "5714,6535,6636,6742", "endColumns": "103,100,105,100", "endOffsets": "5813,6631,6737,6838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "29,30,31,32,33,34,35,72", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2779,2874,2981,3078,3178,3281,3385,7136", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "2869,2976,3073,3173,3276,3380,3491,7232"}}]}]}