name: khatwa_app
description: "خطوة - Khatwa: منصة للاستشارات والدعم لذوي الإعاقة والمختصين"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Icons
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1

  # State Management
  provider: ^6.1.2

  # Navigation
  go_router: ^14.2.7

  # HTTP and API
  http: ^1.2.2
  dio: ^5.7.0

  # Local Storage
  shared_preferences: ^2.3.2
  sqflite: ^2.4.0

  # Media and Video
  video_player: ^2.9.1
  image_picker: ^1.1.2

  # Maps and Location
  google_maps_flutter: ^2.9.0
  geolocator: ^13.0.1
  geocoding: ^3.0.0

  # Chat and Real-time
  socket_io_client: ^2.0.3+1

  # Animations and UI Effects
  lottie: ^3.1.2
  flutter_staggered_animations: ^1.1.1
  shimmer: ^3.0.0

  # Date and Time
  intl: ^0.19.0
  table_calendar: ^3.1.2

  # File handling
  path_provider: ^2.1.4
  file_picker: ^8.1.2

  # Permissions
  permission_handler: ^11.3.1

  # Utils
  uuid: ^4.5.1
  url_launcher: ^6.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets configuration
  assets:
    - assets/images/
    - assets/videos/
    - assets/icons/
    - assets/animations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Arabic Fonts Configuration
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Light.ttf
          weight: 300
        - asset: assets/fonts/Tajawal-Regular.ttf
          weight: 400
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
    - family: Amiri
      fonts:
        - asset: assets/fonts/Amiri-Regular.ttf
          weight: 400
        - asset: assets/fonts/Amiri-Bold.ttf
          weight: 700
