# الخطوط العربية لتطبيق خطوة (Khatwa)

## الخطوط المطلوبة:

### 1. خط Cairo
- **الاستخدام**: الخط الأساسي للتطبيق
- **الملفات المطلوبة**:
  - `Cairo-Regular.ttf`
  - `Cairo-Bold.ttf`
  - `Cairo-Light.ttf`
  - `Cairo-Medium.ttf`
  - `Cairo-SemiBold.ttf`

### 2. خط Tajawal
- **الاستخدام**: خط ثانوي للعناوين والنصوص المهمة
- **الملفات المطلوبة**:
  - `Tajawal-Regular.ttf`
  - `Tajawal-Bold.ttf`
  - `Tajawal-Light.ttf`
  - `Tajawal-Medium.ttf`

### 3. خط Amiri
- **الاستخدام**: خط زخرفي للعناوين الخاصة
- **الملفات المطلوبة**:
  - `Amiri-Regular.ttf`
  - `Amiri-Bold.ttf`

## روابط التحميل:

### Cairo Font
- **Google Fonts**: https://fonts.google.com/specimen/Cairo
- **GitHub**: https://github.com/Gue3bara/Cairo

### Tajawal Font
- **Google Fonts**: https://fonts.google.com/specimen/Tajawal
- **GitHub**: https://github.com/Gue3bara/Tajawal

### Amiri Font
- **Google Fonts**: https://fonts.google.com/specimen/Amiri
- **GitHub**: https://github.com/aliftype/amiri

## تعليمات التثبيت:

1. قم بتحميل الخطوط من الروابط أعلاه
2. ضع ملفات TTF في هذا المجلد
3. تأكد من أن أسماء الملفات تطابق الأسماء المذكورة أعلاه
4. قم بتشغيل `flutter pub get` بعد إضافة الخطوط

## ملاحظات:
- الخطوط محسنة للغة العربية مع دعم RTL
- تدعم جميع الأوزان المطلوبة للتطبيق
- متوافقة مع جميع أنظمة التشغيل
